<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>达人接单管理系统</title>
    <style>
      /* 页面加载时的基础样式 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html, body {
        height: 100%;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        background-color: #f0f2f5;
      }

      #app {
        height: 100vh;
        width: 100vw;
        background-color: #f0f2f5;
      }

      /* 加载骨架屏样式 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f0f2f5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-logo {
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        font-weight: bold;
        animation: pulse 2s infinite;
      }

      .loading-text {
        color: #606266;
        font-size: 14px;
        margin-bottom: 20px;
      }

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #e4e7ed;
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      /* 当Vue应用加载完成后隐藏加载屏 */
      .app-loaded .app-loading {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 加载骨架屏 -->
      <div class="app-loading">
        <div class="loading-logo">达</div>
        <div class="loading-text">达人接单管理系统</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
