{"name": "myexpert", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.8", "myexpert": "file:", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.26.0", "vite": "^6.2.4", "vue-tsc": "^2.2.8"}}