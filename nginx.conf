# Nginx配置文件 - 达人接单平台
# 用于代理前端、小程序和后端API请求

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 访问日志
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # 上游服务器配置
    upstream backend {
        server localhost:9090;  # Spring Boot后端服务
    }
    
    upstream frontend {
        server localhost:3000;  # Vue前端开发服务器
    }

    upstream miniprogram {
        server localhost:9090;  # 小程序专用代理到后端
    }
    
    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 根路径重定向到管理后台
        location = / {
            return 301 /admin/;
        }
        
        # 管理后台前端 (Vue应用)
        location /admin/ {
            proxy_pass http://frontend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 处理Vue Router的history模式
            try_files $uri $uri/ /index.html;
        }
        
        # API接口代理 - 关键配置
        # 将 /api/* 请求代理到后端的 /*
        location /api/ {
            # 重写URL，去掉/api前缀
            rewrite ^/api/(.*)$ /$1 break;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS配置 - 支持小程序和Web端
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
            
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
        
        # 静态资源代理 - 直接代理到后端
        location /static/ {
            proxy_pass http://backend/static/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 静态资源缓存
            expires 30d;
            add_header Cache-Control "public, immutable";
        }
        
        # 文件上传/下载代理
        location /files/ {
            proxy_pass http://backend/files/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 头像文件代理
        location /avatars/ {
            proxy_pass http://backend/avatars/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 头像缓存
            expires 7d;
            add_header Cache-Control "public";
        }
        
        # Swagger文档代理
        location /swagger-ui/ {
            proxy_pass http://backend/swagger-ui/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /v3/api-docs {
            proxy_pass http://backend/v3/api-docs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
    
    # 小程序专用服务器配置 - 3001端口
    server {
        listen 3001;
        server_name localhost;

        # 小程序API接口代理
        location /api/ {
            # 重写URL，去掉/api前缀
            rewrite ^/api/(.*)$ /$1 break;

            proxy_pass http://miniprogram;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 小程序CORS配置
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;

            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        # 静态资源代理
        location /static/ {
            proxy_pass http://miniprogram/static/;
            proxy_set_header Host $host;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # 文件代理
        location /files/ {
            proxy_pass http://miniprogram/files/;
            proxy_set_header Host $host;
        }

        # 头像代理
        location /avatars/ {
            proxy_pass http://miniprogram/avatars/;
            proxy_set_header Host $host;
            expires 7d;
            add_header Cache-Control "public";
        }
    }
}
