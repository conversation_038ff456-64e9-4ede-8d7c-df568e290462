# 生产环境配置
# ================================
# 数据库配置
# ================================
DB_HOST=${DB_HOST}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-expert_db}
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}

# ================================
# Redis配置
# ================================
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DATABASE=${REDIS_DATABASE:-0}

# ================================
# JWT配置
# ================================
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRATION=${JWT_EXPIRATION:-86400000}

# ================================
# 微信小程序配置
# ================================
WECHAT_APPID=${WECHAT_APPID}
WECHAT_SECRET=${WECHAT_SECRET}

# ================================
# 微信支付配置
# ================================
WECHAT_PAY_MCHID=${WECHAT_PAY_MCHID}
WECHAT_PAY_PRIVATE_KEY_PATH=${WECHAT_PAY_PRIVATE_KEY_PATH}
WECHAT_PAY_MERCHANT_SERIAL_NUMBER=${WECHAT_PAY_MERCHANT_SERIAL_NUMBER}
WECHAT_PAY_API_V3_KEY=${WECHAT_PAY_API_V3_KEY}

# ================================
# 阿里云OSS配置
# ================================
OSS_ENDPOINT=${OSS_ENDPOINT}
OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
OSS_BUCKET_NAME=${OSS_BUCKET_NAME}
OSS_DOMAIN=${OSS_DOMAIN}

# ================================
# 应用配置
# ================================
APP_ENV=prod
APP_DOMAIN=${APP_DOMAIN}
UPLOAD_PATH=${UPLOAD_PATH:-/app/uploads/}
UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE:-10485760}

# ================================
# 日志配置
# ================================
LOG_FILE_ENABLED=${LOG_FILE_ENABLED:-true}
LOG_FILE_PATH=${LOG_FILE_PATH:-/app/logs/}
LOG_LEVEL=${LOG_LEVEL:-warn}

# ================================
# 邮件配置
# ================================
MAIL_HOST=${MAIL_HOST}
MAIL_PORT=${MAIL_PORT:-587}
MAIL_USERNAME=${MAIL_USERNAME}
MAIL_PASSWORD=${MAIL_PASSWORD}
MAIL_FROM=${MAIL_FROM}

# ================================
# 短信配置
# ================================
SMS_ACCESS_KEY_ID=${SMS_ACCESS_KEY_ID}
SMS_ACCESS_KEY_SECRET=${SMS_ACCESS_KEY_SECRET}
SMS_SIGN_NAME=${SMS_SIGN_NAME}
SMS_TEMPLATE_CODE=${SMS_TEMPLATE_CODE}
