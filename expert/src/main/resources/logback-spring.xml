<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用程序日志 -->
    <logger name="com.qing.expert" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 关闭框架日志 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="com.mysql.cj" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>

    <!-- 根日志配置 -->
    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>
