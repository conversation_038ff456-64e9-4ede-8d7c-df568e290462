/* 组件样式 */

/* 卡片组件 - 增强玻璃拟态效果 */
.card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  -webkit-backdrop-filter: blur($blur-base);
  backdrop-filter: blur($blur-base);
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-card, $box-shadow-inner-glow;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  transition: all $transition-base $ease-smooth;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }

  &:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: $box-shadow-sm;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
  }

  &.card-hover {
    &:hover {
      box-shadow: $box-shadow-float, $box-shadow-outer-glow;
      transform: translateY(-4rpx) scale(1.02);
      background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
    }
  }

  &.card-glass {
    background: $bg-glass-white;
    -webkit-backdrop-filter: blur($blur-lg);
    backdrop-filter: blur($blur-lg);
    box-shadow: $box-shadow-glass;
    border: 1rpx solid rgba(255, 255, 255, 0.2);
  }

  &.card-neumorphism {
    background: $bg-neumorphism;
    box-shadow: $box-shadow-neumorphism-sm;
    border: none;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;

    &:active {
      box-shadow: $box-shadow-neumorphism-inset;
      transform: translateY(1rpx);
    }
  }

  &.card-bordered {
    border: 1rpx solid rgba(255, 255, 255, 0.4);
    box-shadow: $box-shadow-glass-sm;
  }

  &.card-flat {
    box-shadow: none;
    border: 1rpx solid $border-color-light;
    background: rgba(255, 255, 255, 0.6);
    -webkit-backdrop-filter: blur($blur-sm);
    backdrop-filter: blur($blur-sm);
  }
}

.card-header {
  padding: $spacing-lg;
  border-bottom: 1rpx solid $border-color-light;

  .card-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-color-primary;
    margin-bottom: $spacing-xs;
  }

  .card-subtitle {
    font-size: $font-size-sm;
    color: $text-color-secondary;
  }
}

.card-body {
  padding: $spacing-lg;
}

.card-footer {
  padding: $spacing-lg;
  border-top: 1rpx solid $border-color-light;
  background-color: $bg-color-light;
}

/* 按钮组件 - 现代化设计 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-xl;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all $transition-base $ease-spring;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5rpx;

  // 按钮发光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  &:active::before {
    left: 100%;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  &:active {
    transform: translateY(1rpx) scale(0.98);
  }

  // 主要按钮 - 玻璃拟态风格
  &.btn-primary {
    background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
    color: $text-color-white;
    box-shadow: $box-shadow-base, inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.2);

    &:active {
      background: linear-gradient(135deg, $primary-dark 0%, $primary-color 100%);
      box-shadow: $box-shadow-sm;
    }

    &:hover {
      box-shadow: $box-shadow-lg, $box-shadow-outer-glow;
      transform: translateY(-2rpx);
    }
  }

  // 次要按钮 - 新拟物化风格
  &.btn-secondary {
    background: $bg-neumorphism;
    color: $secondary-color;
    box-shadow: $box-shadow-neumorphism-sm;
    border: none;

    &:active {
      box-shadow: $box-shadow-neumorphism-inset;
      transform: translateY(1rpx);
    }

    &:hover {
      background: $bg-neumorphism-light;
    }
  }

  // 成功按钮
  &.btn-success {
    background-color: $success-color;
    color: $text-color-white;

    &:active {
      background-color: $success-dark;
    }
  }

  // 警告按钮
  &.btn-warning {
    background-color: $warning-color;
    color: $text-color-white;

    &:active {
      background-color: $warning-dark;
    }
  }

  // 错误按钮
  &.btn-error {
    background-color: $error-color;
    color: $text-color-white;

    &:active {
      background-color: $error-dark;
    }
  }

  // 信息按钮
  &.btn-info {
    background-color: $info-color;
    color: $text-color-white;

    &:active {
      background-color: $info-dark;
    }
  }

  // 轮廓按钮
  &.btn-outline {
    background-color: transparent;
    border: 1rpx solid $border-color;
    color: $text-color-primary;

    &:active {
      background-color: $bg-color-light;
    }

    &.btn-outline-primary {
      border-color: $primary-color;
      color: $primary-color;

      &:active {
        background-color: rgba($primary-color, 0.1);
      }
    }

    &.btn-outline-secondary {
      border-color: $secondary-color;
      color: $secondary-color;

      &:active {
        background-color: rgba($secondary-color, 0.1);
      }
    }
  }

  // 文字按钮
  &.btn-text {
    background-color: transparent;
    color: $primary-color;
    padding: $spacing-xs $spacing-sm;

    &:active {
      background-color: rgba($primary-color, 0.1);
    }
  }

  // 按钮尺寸
  &.btn-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    border-radius: $border-radius-sm;
  }

  &.btn-lg {
    padding: $spacing-lg $spacing-xl;
    font-size: $font-size-lg;
    border-radius: $border-radius-lg;
  }

  &.btn-xl {
    padding: $spacing-xl $spacing-2xl;
    font-size: $font-size-xl;
    border-radius: $border-radius-xl;
  }

  // 圆形按钮
  &.btn-round {
    border-radius: $border-radius-full;
  }

  // 块级按钮
  &.btn-block {
    width: 100%;
    display: flex;
  }
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: $spacing-sm $spacing-base;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $bg-color-white;
  transition: all $transition-base;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2rpx rgba($primary-color, 0.2);
    outline: none;
  }

  &::placeholder {
    color: $text-color-placeholder;
  }

  &:disabled {
    background-color: $bg-color-light;
    color: $text-color-disabled;
    cursor: not-allowed;
  }

  &.input-error {
    border-color: $error-color;

    &:focus {
      border-color: $error-color;
      box-shadow: 0 0 0 2rpx rgba($error-color, 0.2);
    }
  }

  &.input-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }

  &.input-lg {
    padding: $spacing-lg $spacing-xl;
    font-size: $font-size-lg;
  }
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: 1;

  &.tag-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &.tag-secondary {
    background-color: rgba($secondary-color, 0.1);
    color: $secondary-color;
  }

  &.tag-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }

  &.tag-warning {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }

  &.tag-error {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }

  &.tag-info {
    background-color: rgba($info-color, 0.1);
    color: $info-color;
  }

  &.tag-gray {
    background-color: $neutral-100;
    color: $neutral-600;
  }

  &.tag-sm {
    padding: 4rpx $spacing-xs;
    font-size: 18rpx;
  }

  &.tag-lg {
    padding: $spacing-sm $spacing-base;
    font-size: $font-size-sm;
  }
}

/* 头像组件 */
.avatar {
  display: inline-block;
  border-radius: $border-radius-full;
  overflow: hidden;
  background-color: $bg-color-light;

  &.avatar-sm {
    width: 60rpx;
    height: 60rpx;
  }

  &.avatar-base {
    width: 80rpx;
    height: 80rpx;
  }

  &.avatar-lg {
    width: 120rpx;
    height: 120rpx;
  }

  &.avatar-xl {
    width: 160rpx;
    height: 160rpx;
  }

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 $spacing-xs;
  border-radius: $border-radius-full;
  font-size: 20rpx;
  font-weight: $font-weight-medium;
  line-height: 1;
  color: $text-color-white;
  background-color: $error-color;

  &.badge-primary {
    background-color: $primary-color;
  }

  &.badge-secondary {
    background-color: $secondary-color;
  }

  &.badge-success {
    background-color: $success-color;
  }

  &.badge-warning {
    background-color: $warning-color;
  }

  &.badge-info {
    background-color: $info-color;
  }

  &.badge-sm {
    min-width: 24rpx;
    height: 24rpx;
    font-size: 18rpx;
  }

  &.badge-lg {
    min-width: 40rpx;
    height: 40rpx;
    font-size: 24rpx;
  }
}
