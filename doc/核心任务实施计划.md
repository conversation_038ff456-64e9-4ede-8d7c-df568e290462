# 核心任务实施计划

## 🎯 任务概述

根据您的要求，专注完成以下三个核心任务：
1. **移除支付宝支付功能**
2. **完善微信支付回调接口**
3. **完善评价系统开发**

## 📋 任务一：移除支付宝支付功能

### 🔍 当前状态分析
- PaymentServiceImpl.alipay()方法存在但未实现
- PaymentTypeEnum包含ALIPAY枚举
- 前端可能有支付宝支付选项

### ✅ 具体实施步骤

#### 1. 后端代码清理（0.5天）
```java
// 需要修改的文件：
- expert/src/main/java/com/qing/expert/service/impl/PaymentServiceImpl.java
  - 移除alipay()方法实现
  - 在createPayment()中移除ALIPAY分支

- expert/src/main/java/com/qing/expert/enums/PaymentTypeEnum.java
  - 移除ALIPAY枚举项

- expert/src/main/java/com/qing/expert/controller/user/UserPaymentController.java
  - 移除支付宝回调接口alipayCallback()
```

#### 2. 前端代码清理（0.5天）
```typescript
// 需要检查和清理的文件：
- uniapp/Uni_expert/pages/order/payment.vue
- web/src/views/orders/payment.vue
- 移除支付宝支付选项和相关逻辑
```

## 📋 任务二：完善微信支付回调接口

### 🔍 当前状态分析
- WechatPayServiceImpl.handlePaymentNotify()方法标记为TODO
- 缺少APIv3回调签名验证
- 缺少支付状态同步机制

### ✅ 具体实施步骤

#### 1. 实现回调验证逻辑（1天）
```java
// 文件：expert/src/main/java/com/qing/expert/service/impl/WechatPayServiceImpl.java

@Override
public boolean handlePaymentNotify(String jsonData) {
    try {
        // 1. 解析回调JSON数据
        JSONObject notifyData = JSON.parseObject(jsonData);
        
        // 2. 验证回调签名（使用微信支付SDK）
        if (!verifyNotifySignature(jsonData)) {
            log.error("微信支付回调签名验证失败");
            return false;
        }
        
        // 3. 解析支付结果
        String outTradeNo = notifyData.getString("out_trade_no");
        String transactionId = notifyData.getString("transaction_id");
        String tradeState = notifyData.getString("trade_state");
        
        // 4. 更新支付状态
        return updatePaymentStatus(outTradeNo, transactionId, tradeState);
        
    } catch (Exception e) {
        log.error("处理微信支付回调失败", e);
        return false;
    }
}
```

#### 2. 实现支付状态同步（1天）
```java
// 新增方法：updatePaymentStatus()
private boolean updatePaymentStatus(String paymentNo, String transactionId, String tradeState) {
    // 1. 查询支付记录
    // 2. 更新支付状态
    // 3. 更新订单状态
    // 4. 发送支付成功消息
    // 5. 记录交易流水
}
```

#### 3. 添加异常处理和重试机制（0.5天）
```java
// 添加回调重试机制
// 添加异常情况处理
// 添加日志记录
```

## 📋 任务三：完善评价系统开发

### 🔍 当前状态分析
- 数据库表sys_evaluation已设计完成
- 完全没有相关的Controller、Service、Mapper
- 前端评价页面未开发

### ✅ 具体实施步骤

#### 1. 后端评价接口开发（2天）

##### 1.1 创建实体类和DTO
```java
// 文件：expert/src/main/java/com/qing/expert/entity/Evaluation.java
// 文件：expert/src/main/java/com/qing/expert/dto/EvaluationSaveDTO.java
// 文件：expert/src/main/java/com/qing/expert/dto/EvaluationQueryDTO.java
// 文件：expert/src/main/java/com/qing/expert/vo/EvaluationVO.java
```

##### 1.2 创建Mapper接口
```java
// 文件：expert/src/main/java/com/qing/expert/mapper/EvaluationMapper.java
public interface EvaluationMapper extends BaseMapper<Evaluation> {
    // 查询达人评价列表
    IPage<EvaluationVO> selectEvaluationPage(Page<EvaluationVO> page, EvaluationQueryDTO queryDTO);
    
    // 计算达人平均评分
    BigDecimal calculateExpertRating(Long expertId);
    
    // 获取评价统计
    EvaluationStatsVO getEvaluationStats(Long expertId);
}
```

##### 1.3 创建Service接口和实现
```java
// 文件：expert/src/main/java/com/qing/expert/service/EvaluationService.java
// 文件：expert/src/main/java/com/qing/expert/service/impl/EvaluationServiceImpl.java

public interface EvaluationService extends IService<Evaluation> {
    // 提交评价
    Boolean submitEvaluation(EvaluationSaveDTO saveDTO);
    
    // 获取达人评价列表
    IPage<EvaluationVO> getEvaluationPage(EvaluationQueryDTO queryDTO);
    
    // 获取评价详情
    EvaluationVO getEvaluationDetail(Long evaluationId);
    
    // 达人回复评价
    Boolean replyEvaluation(Long evaluationId, String replyContent);
    
    // 更新达人评分
    Boolean updateExpertRating(Long expertId);
}
```

##### 1.4 创建Controller
```java
// 文件：expert/src/main/java/com/qing/expert/controller/api/EvaluationController.java
// 用户端评价接口

// 文件：expert/src/main/java/com/qing/expert/controller/admin/AdminEvaluationController.java
// 管理端评价接口
```

#### 2. 前端评价功能开发（2天）

##### 2.1 小程序端评价功能
```vue
<!-- 文件：uniapp/Uni_expert/pages/order/evaluate.vue -->
<!-- 订单评价提交页面 -->

<!-- 文件：uniapp/Uni_expert/pages/expert/evaluations.vue -->
<!-- 达人评价展示页面 -->

<!-- 文件：uniapp/Uni_expert/pages/user/my-evaluations.vue -->
<!-- 我的评价记录页面 -->
```

##### 2.2 管理端评价功能
```vue
<!-- 文件：web/src/views/evaluations/index.vue -->
<!-- 评价管理列表页面 -->

<!-- 文件：web/src/views/evaluations/detail.vue -->
<!-- 评价详情页面 -->
```

#### 3. 评价业务逻辑完善（1天）

##### 3.1 订单完成后评价提醒
```java
// 在OrderServiceImpl.finishOrder()方法中添加评价提醒逻辑
// 发送评价提醒消息
```

##### 3.2 评价内容审核
```java
// 添加敏感词过滤
// 添加评价内容长度限制
// 添加图片内容审核
```

##### 3.3 防重复评价机制
```java
// 检查用户是否已对该订单评价
// 添加唯一索引约束
```

## 📅 时间安排

### 第1天：支付功能清理
- ✅ 上午：移除支付宝支付后端代码
- ✅ 下午：清理前端支付宝支付相关代码

### 第2-3天：微信支付回调完善
- ✅ 第2天：实现回调验证和状态同步逻辑
- ✅ 第3天：添加异常处理和测试验证

### 第4-7天：评价系统开发
- ✅ 第4天：后端实体类、Mapper、Service开发
- ✅ 第5天：后端Controller接口开发
- ✅ 第6天：小程序端评价功能开发
- ✅ 第7天：管理端评价功能和业务逻辑完善

## 🎯 验收标准

### 支付功能清理
- [ ] 代码中不再包含支付宝支付相关逻辑
- [ ] 前端支付页面只显示微信支付和余额支付
- [ ] 支付流程测试正常

### 微信支付回调
- [ ] 回调接口能正确验证微信签名
- [ ] 支付成功后订单状态正确更新
- [ ] 支付失败情况正确处理
- [ ] 回调日志记录完整

### 评价系统
- [ ] 用户可以对完成的订单进行评价
- [ ] 达人评分能够实时更新
- [ ] 管理员可以查看和管理所有评价
- [ ] 评价内容过滤机制正常工作
- [ ] 防重复评价机制有效

## ⚠️ 注意事项

1. **微信支付测试**：需要配置真实的商户号和证书
2. **数据库变更**：评价系统可能需要调整数据库表结构
3. **前端适配**：确保小程序端和管理端界面适配
4. **性能考虑**：评价查询需要考虑分页和索引优化
5. **安全防护**：评价内容需要防止XSS攻击和敏感词过滤
