# 达人接单平台完整文档

## 📋 项目概述

这是一个基于**Spring Boot 3.5.0 + UniApp + Vue3**的达人服务接单平台，包含**用户（顾客）、达人、管理员**三个角色的完整业务系统。项目采用现代化技术栈，实现了服务发布、订单管理、支付结算、消息推送等完整功能。

**项目整体完成度: 75-80%** ✅ 核心功能已实现，具备上线运行条件

### 角色定义
- **用户（顾客）**：发起合作需求，下单支付
- **达人**：提供服务，接单完成任务
- **管理员**：平台管理，订单分配

## 🏗️ 技术架构

### 后端技术栈 ✅ **完成度: 90%**
- **框架**: Spring Boot 3.5.0 + Java 17
- **数据库**: MySQL 8.0 + Redis 7.0 + MyBatis Plus 3.5.5
- **安全认证**: Spring Security + JWT
- **API文档**: Swagger OpenAPI 3
- **文件存储**: 本地存储/阿里云OSS（可切换）
- **消息推送**: WebSocket + 微信小程序订阅消息

### 前端技术栈 ✅ **完成度: 70%**
- **管理端**: Vue 3 + TypeScript + Element Plus + Pinia + Vite
- **小程序端**: UniApp + Vue 3 + TypeScript + Pinia
- **UI设计**: 现代化玻璃拟态 + 新拟物化设计
- **构建工具**: Vite + HBuilderX

### 支付与配置 ✅ **完成度: 85%**
- **支付方式**: 微信支付 + 支付宝支付 + 余额支付
- **配置管理**: 数据库动态配置（管理员可修改）
- **文件存储**: 支持本地存储和阿里云OSS切换

## 📁 项目结构

```
expert/                           # 后端项目 ✅ 完成度: 90%
├── src/main/java/com/qing/expert/
│   ├── controller/              # 控制器层
│   │   ├── admin/              # 管理端控制器 ✅
│   │   ├── api/                # 用户端API控制器 ✅
│   │   ├── PublicApiController.java  # 公共API ✅
│   │   └── TestController.java      # 测试接口 ✅
│   ├── service/                # 服务层 ✅
│   ├── mapper/                # 数据访问层 ✅
│   ├── entity/                # 实体类 ✅
│   ├── dto/                   # 数据传输对象 ✅
│   ├── vo/                    # 视图对象 ✅
│   ├── common/                # 公共组件 ✅
│   ├── config/                # 配置类 ✅
│   └── util/                  # 工具类 ✅
├── src/main/resources/
│   ├── mapper/                # MyBatis XML映射 ✅
│   ├── application.yml        # 配置文件 ✅
│   └── logback-spring.xml     # 日志配置 ✅
└── pom.xml                    # Maven配置 ✅

web/myexpert/                  # 前端管理项目 🚧 完成度: 70%
├── src/
│   ├── views/                 # 页面组件 🚧
│   ├── components/            # 公共组件 ✅
│   ├── router/                # 路由配置 ✅
│   ├── store/                 # 状态管理 ✅
│   ├── api/                   # API接口 ✅
│   ├── utils/                 # 工具函数 ✅
│   └── assets/                # 静态资源 ✅
├── package.json               # 依赖配置 ✅
└── vite.config.ts            # 构建配置 ✅

uniapp/Uni_expert/            # 小程序项目 ✅ 完成度: 85%
├── pages/                    # 页面文件 ✅
│   ├── index/               # 首页 ✅
│   ├── auth/                # 认证页面 ✅
│   ├── expert/              # 达人相关 ✅
│   ├── order/               # 订单相关 ✅
│   ├── user/                # 用户中心 ✅
│   ├── category/            # 分类页面 ✅
│   └── common/              # 公共页面 ✅
├── api/                     # API接口 ✅
├── components/              # 公共组件 ✅
├── store/                   # 状态管理 ✅
├── utils/                   # 工具函数 ✅
├── static/                  # 静态资源 ✅
├── styles/                  # 样式文件 ✅
├── App.vue                  # 应用入口 ✅
├── main.js                  # 主文件 ✅
├── pages.json               # 页面配置 ✅
├── manifest.json            # 应用配置 ✅
└── uni.scss                 # 样式变量 ✅
```

## 🗄️ 数据库设计

### 开发环境数据库连接
- **账号**: root
- **密码**: 1234

### 核心数据表

#### 1. 用户表 (sys_user)
```sql
CREATE TABLE `sys_user` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `openid` varchar(100) UNIQUE COMMENT '微信openid',
  `nickname` varchar(50) COMMENT '昵称',
  `avatar` varchar(500) COMMENT '头像URL',
  `phone` varchar(20) COMMENT '手机号',
  `wechat_no` varchar(50) COMMENT '微信号',
  `role_type` tinyint DEFAULT 1 COMMENT '角色类型:1用户 2达人 3管理员',
  `status` tinyint DEFAULT 1 COMMENT '状态:0禁用 1正常',
  `balance` decimal(10,2) DEFAULT 0 COMMENT '余额',
  `last_login_time` datetime COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='用户表';
```

#### 2. 达人表 (sys_talent)
```sql
CREATE TABLE `sys_talent` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `category_id` bigint COMMENT '分类ID',
  `talent_name` varchar(100) COMMENT '达人名称',
  `description` text COMMENT '详细描述',
  `service_price` decimal(10,2) COMMENT '服务价格',
  `talent_avatar` varchar(500) COMMENT '达人头像',
  `contact_info` varchar(200) COMMENT '联系方式',
  `status` tinyint DEFAULT 0 COMMENT '状态:0待审核 1正常 2忙碌 3禁用',
  `order_count` int DEFAULT 0 COMMENT '接单数量',
  `rating` decimal(3,2) DEFAULT 5.0 COMMENT '评分',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='达人表';
```

#### 3. 订单表 (sys_order)
```sql
CREATE TABLE `sys_order` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `order_no` varchar(32) UNIQUE NOT NULL COMMENT '订单编号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `talent_id` bigint COMMENT '达人ID',
  `category_id` bigint COMMENT '分类ID',
  `service_name` varchar(200) COMMENT '服务名称',
  `service_price` decimal(10,2) COMMENT '服务价格',
  `task_description` text COMMENT '任务描述',
  `expected_time` datetime COMMENT '期望完成时间',
  `order_status` tinyint DEFAULT 1 COMMENT '订单状态:1待接单 2服务中 3已完成 4售后中 5已取消',
  `payment_status` tinyint DEFAULT 0 COMMENT '支付状态:0未支付 1已支付 2已退款',
  `user_signature` varchar(500) COMMENT '用户签名图片',
  `talent_signature` varchar(500) COMMENT '达人签名图片',
  `completion_screenshots` json COMMENT '完成截图',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `accept_time` datetime COMMENT '接单时间',
  `complete_time` datetime COMMENT '完成时间',
  `cancel_time` datetime COMMENT '取消时间'
) COMMENT='订单表';
```

## ⚙️ 系统配置

### 微信小程序配置
- **AppID**: wx52c058048a9d15bb
- **AppSecret**: 9c9b2613c4a8193ff1d746ca366b5aa7
- **配置方式**: 通过管理端动态配置（存储在 system_configs 表）

### 环境变量配置

#### 必需的环境变量
| 变量名 | 描述 | 示例 | 安全级别 |
|--------|------|------|----------|
| `DB_PASSWORD` | 数据库密码 | `your_secure_password` | 🔴 高危 |
| `JWT_SECRET` | JWT签名密钥 | `64位随机字符串` | 🔴 高危 |
| `WECHAT_SECRET` | 微信小程序密钥 | `微信后台获取` | 🔴 高危 |
| `REDIS_PASSWORD` | Redis密码 | `redis_password` | 🟡 中危 |

#### 可选的环境变量
| 变量名 | 描述 | 默认值 | 安全级别 |
|--------|------|--------|----------|
| `DB_HOST` | 数据库主机 | `localhost` | 🟢 低危 |
| `DB_PORT` | 数据库端口 | `3306` | 🟢 低危 |
| `REDIS_HOST` | Redis主机 | `localhost` | 🟢 低危 |
| `OSS_ACCESS_KEY_ID` | 阿里云OSS密钥ID | - | 🟡 中危 |
| `OSS_ACCESS_KEY_SECRET` | 阿里云OSS密钥 | - | 🔴 高危 |

### 前端环境配置

#### Web管理端环境变量
- **VITE_API_BASE_URL**: API服务的基础URL
  - 开发环境: `http://localhost:9090`
  - 生产环境: `https://your-domain.com`
- **VITE_STATIC_BASE_URL**: 静态资源的基础URL
  - 开发环境: `http://localhost:9090`
  - 生产环境: `https://your-domain.com` 或 CDN地址

#### UniApp小程序配置
- **AppID**: wx52c058048a9d15bb（在manifest.json中配置）
- **API接口地址**:
  - 开发环境: `http://localhost:9090`
  - 生产环境: 配置在 `api/request.ts` 中

## 🔐 安全配置

### JWT安全配置
- JWT密钥长度至少32位
- 令牌有效期设置合理
- 支持令牌黑名单机制

### 数据库安全
- 数据库连接使用SSL
- 敏感数据加密存储
- 定期备份重要数据

### 文件存储安全
- 文件上传大小限制
- 文件类型白名单验证
- 文件访问权限控制

## 📊 日志配置

### 日志级别
- **ERROR**: 系统错误，需要立即处理
- **WARN**: 警告信息，可能存在问题
- **INFO**: 重要的业务信息
- **DEBUG**: 调试信息，开发时使用

### 日志文件配置
- 支持彩色日志输出（20多种颜色类型）
- 可配置的日志文件开关
- 动态配置管理，运行时可调整
- 多环境支持（开发/测试/生产）
- 高性能异步日志

### 日志文件开关使用
- **开发环境**: 关闭文件日志 (`file-enabled=false`)，减少磁盘IO
- **生产环境**: 启用所有日志文件，便于问题排查
- **临时调试**: 使用API接口动态开关，无需重启应用

## 💳 微信支付配置

### 支付配置开关
- **配置键**: `wechat_pay_config_enabled`
- **默认值**: false（禁用微信支付配置验证）
- **true**: 启用微信支付配置验证，需要完整配置微信支付参数
- **false**: 禁用验证，应用可以在未配置微信支付时正常启动

### 微信支付完整配置清单
1. **商户号** (`wechat_pay_mch_id`)
2. **API V3密钥** (`wechat_pay_api_v3_key`)
3. **证书序列号** (`wechat_pay_cert_serial_no`)
4. **私钥文件路径** (`wechat_pay_private_key_path`)
5. **小程序AppID** (`wechat_miniapp_app_id`)

### 微信支付申请要求
- 已认证的小程序（APPID）
- 微信支付商户号
- JSAPI支付权限
- 完整的开发配置

## 🚀 部署指南

### 默认登录信息
#### 管理员账号
- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员

#### 登录地址
- **管理端**: http://localhost:3030/login
- **后端API**: http://localhost:9090/api

### 启动步骤
1. **数据库准备**
   - 创建MySQL数据库
   - 导入数据库脚本
   - 配置Redis服务

2. **后端启动**
   ```bash
   cd expert
   mvn clean package
   java -jar target/expert-1.0.0.jar
   ```

3. **前端管理端启动**
   ```bash
   cd web
   npm install
   npm run dev
   ```

4. **小程序端**
   - 使用HBuilderX打开uniapp/Uni_expert项目
   - 配置微信小程序AppID
   - 运行到微信开发者工具

### 生产环境部署
- **服务器**: 阿里云ECS
- **数据库**: 阿里云RDS MySQL
- **缓存**: 阿里云Redis
- **文件存储**: 阿里云OSS
- **域名**: 已备案域名
- **SSL**: 免费SSL证书

## 🔧 开发指南

### 环境要求
- **后端**: JDK 17+, Maven 3.6+
- **前端**: Node.js 16+, npm/yarn
- **小程序**: HBuilderX 3.0+, 微信开发者工具

### 开发流程
1. 克隆项目到本地
2. 配置数据库连接
3. 启动后端服务
4. 启动前端项目
5. 配置小程序AppID
6. 开始开发调试

### API接口规范
- **管理端接口**: `/admin/**`
- **用户端接口**: `/api/**`
- **公共接口**: `/banners`, `/announcements`, `/categories`
- **静态资源**: `/static/**`, `/avatars/**`, `/photos/**`

## 📈 项目里程碑

- **✅ 里程碑1**: 基础架构搭建完成
- **✅ 里程碑2**: 核心业务模块完成
- **🚧 里程碑3**: 前端系统开发（进行中 - 70%）
- **🚧 里程碑4**: 小程序端完善（进行中 - 85%）
- **📋 里程碑5**: 系统测试优化（待开始）
- **📋 里程碑6**: 生产环境部署（待开始）

## 💡 技术亮点

1. **动态配置管理** - 系统配置可通过管理端动态修改
2. **多端统一架构** - 后端API支持管理端、小程序端、达人端
3. **现代化UI设计** - 玻璃拟态、新拟物化设计风格
4. **完整的业务流程** - 从用户注册到订单完成的完整闭环
5. **灵活的支付方式** - 支持微信支付、支付宝、余额支付
6. **智能消息推送** - 订单状态变更自动通知、模板消息
7. **完善的权限控制** - JWT认证、角色权限、接口权限

## 📋 项目待完成计划

### 🔥 高优先级任务（必须完成）

#### 1. 支付回调接口完善 🚧 **进度: 30%**
**当前状态**: 微信支付基础框架已搭建，回调处理逻辑待完善

**待完成任务**:
- [ ] **微信支付回调处理完善**
  - 实现微信支付APIv3回调验证逻辑
  - 完善回调签名验证机制
  - 实现支付状态同步更新
  - 添加回调异常处理和重试机制
  - 完善支付成功后的业务逻辑（订单状态更新、消息通知等）

- [ ] **移除支付宝支付相关代码**
  - 清理PaymentServiceImpl中的alipay()方法实现
  - 移除支付宝相关的配置和依赖
  - 更新支付类型枚举，只保留微信支付和余额支付
  - 清理前端支付宝支付相关代码

- [ ] **余额支付流程优化**
  - 完善余额不足的错误提示
  - 优化余额支付的事务处理
  - 添加余额变动记录功能
  - 完善余额充值接口

**预计工作量**: 2-3天

#### 2. 消息推送系统 🚧 **进度: 40%**
**当前状态**: 消息记录功能已完成，微信模板消息和WebSocket推送待实现

**待完成任务**:
- [ ] **微信模板消息实现**
  - 完成WechatTemplateMessageService实现类
  - 配置微信模板消息模板
  - 实现订单状态变更通知
  - 实现支付成功通知
  - 实现提现审核通知

- [ ] **WebSocket实时推送**
  - 搭建WebSocket服务端
  - 实现客户端连接管理
  - 完成实时消息推送
  - 添加消息确认机制

- [ ] **小程序订阅消息**
  - 配置小程序订阅消息模板
  - 实现订阅消息发送
  - 处理用户授权状态
  - 优化消息发送策略

**预计工作量**: 4-5天

#### 2. 评价系统完善开发 📋 **进度: 0%**
**当前状态**: 数据库表sys_evaluation已设计完成，功能完全未实现

**待完成任务**:
- [ ] **后端评价接口开发**
  - 创建评价控制器(EvaluationController)
    - 用户提交评价接口
    - 获取达人评价列表接口
    - 管理端评价管理接口
  - 实现评价服务类(EvaluationService)
    - 评价CRUD基础操作
    - 评价统计计算逻辑
    - 达人评分自动更新机制
  - 创建评价数据访问层(EvaluationMapper)
    - 评价查询映射
    - 评价统计查询
    - 达人评分计算查询

- [ ] **前端评价功能开发**
  - **管理端评价管理**
    - 评价列表页面（支持筛选、搜索）
    - 评价详情查看页面
    - 评价审核功能（删除不当评价）
    - 评价统计报表页面
  - **小程序端评价功能**
    - 订单评价提交页面（星级评分、文字评价、图片上传）
    - 达人详情页评价展示
    - 我的评价记录页面
    - 评价成功提示页面

- [ ] **评价业务逻辑完善**
  - 订单完成后自动触发评价提醒
  - 评价内容敏感词过滤
  - 防止重复评价机制
  - 达人回复评价功能
  - 评价对达人评分的实时影响
  - 评价数据统计和分析

**预计工作量**: 4-5天

### 🔧 中优先级任务（重要功能）

#### 3. 消息推送系统完善 🚧 **进度: 40%**
**当前状态**: 消息记录功能已完成，微信模板消息和WebSocket推送待实现

**详细实施计划**:
- [ ] **微信模板消息实现**（2天）
  ```java
  // 创建实现类：WechatTemplateMessageServiceImpl
  // 文件：expert/src/main/java/com/qing/expert/service/impl/WechatTemplateMessageServiceImpl.java

  @Service
  public class WechatTemplateMessageServiceImpl implements WechatTemplateMessageService {
      // 实现sendTemplateMessage()方法
      // 实现getAccessToken()方法
      // 实现各种消息模板构建方法
  }
  ```
  - 配置微信模板消息模板ID
  - 实现订单状态变更通知
  - 实现支付成功通知
  - 实现提现审核通知
  - 添加模板消息发送失败重试机制

- [ ] **WebSocket实时推送**（2天）
  ```java
  // 创建WebSocket配置类
  // 文件：expert/src/main/java/com/qing/expert/config/WebSocketConfig.java

  // 创建WebSocket处理器
  // 文件：expert/src/main/java/com/qing/expert/websocket/MessageWebSocketHandler.java

  // 创建连接管理器
  // 文件：expert/src/main/java/com/qing/expert/websocket/WebSocketConnectionManager.java
  ```
  - 搭建WebSocket服务端配置
  - 实现客户端连接管理（用户上线/下线）
  - 完成实时消息推送功能
  - 添加消息确认和重发机制
  - 实现管理端实时通知功能

- [ ] **小程序订阅消息**（1天）
  ```javascript
  // 小程序端订阅消息处理
  // 文件：uniapp/Uni_expert/utils/subscribe-message.js

  // 订阅消息模板配置
  const TEMPLATE_IDS = {
    ORDER_STATUS: 'template_id_1',
    PAYMENT_SUCCESS: 'template_id_2'
  }
  ```
  - 配置小程序订阅消息模板
  - 实现用户授权订阅流程
  - 处理订阅消息发送逻辑
  - 优化消息发送策略（批量发送、失败重试）

**预计工作量**: 5天

#### 4. 前端管理系统完善 🚧 **进度: 70%**
**当前状态**: 基础框架完成，部分业务页面待开发

**详细实施计划**:
- [ ] **订单管理页面完善**（2天）
  ```vue
  <!-- 文件：web/src/views/orders/index.vue -->
  <!-- 订单列表页面优化 -->

  <!-- 文件：web/src/views/orders/detail.vue -->
  <!-- 订单详情页面 -->

  <!-- 文件：web/src/views/orders/statistics.vue -->
  <!-- 订单统计报表页面 -->
  ```
  - 订单列表页面功能优化（高级筛选、批量操作）
  - 订单详情页面完善（完整信息展示、状态操作）
  - 订单状态流转管理（状态变更日志、操作权限）
  - 订单统计报表（图表展示、数据导出）

- [ ] **达人管理页面开发**（2天）
  ```vue
  <!-- 文件：web/src/views/experts/audit.vue -->
  <!-- 达人审核页面 -->

  <!-- 文件：web/src/views/experts/detail.vue -->
  <!-- 达人详情页面 -->

  <!-- 文件：web/src/views/experts/statistics.vue -->
  <!-- 达人业绩统计页面 -->
  ```
  - 达人审核页面（申请列表、审核操作、审核记录）
  - 达人详情页面（完整信息、服务记录、评价统计）
  - 达人状态管理（在线状态、服务状态、账户状态）
  - 达人业绩统计（收入统计、订单统计、评分趋势）

- [ ] **财务管理页面开发**（2天）
  ```vue
  <!-- 文件：web/src/views/finance/income.vue -->
  <!-- 收入统计页面 -->

  <!-- 文件：web/src/views/finance/withdraw.vue -->
  <!-- 提现管理页面 -->

  <!-- 文件：web/src/views/finance/reports.vue -->
  <!-- 财务报表页面 -->
  ```
  - 收入统计页面（平台收入、达人收入、趋势分析）
  - 提现管理页面（提现申请、审核流程、提现记录）
  - 财务报表页面（收支明细、利润分析、对账功能）
  - 资金流水记录（交易流水、资金变动、异常监控）

**预计工作量**: 6天

#### 5. 小程序端功能完善 🚧 **进度: 85%**
**当前状态**: 主要功能已完成，部分细节功能待完善

**详细实施计划**:
- [ ] **达人工作台优化**（2天）
  ```vue
  <!-- 文件：uniapp/Uni_expert/pages/expert/workspace.vue -->
  <!-- 达人工作台主页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/expert/earnings.vue -->
  <!-- 收益统计页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/expert/settings.vue -->
  <!-- 达人设置页面 -->
  ```
  - 收益统计图表（日/周/月收益趋势、订单统计图表）
  - 订单管理优化（订单筛选、快速操作、批量处理）
  - 工作状态切换（在线/忙碌/离线状态管理）
  - 服务时间设置（工作时间段、休息时间、特殊日期）
  - 达人资料管理（技能标签、服务介绍、照片管理）

- [ ] **用户中心完善**（2天）
  ```vue
  <!-- 文件：uniapp/Uni_expert/pages/user/profile-edit.vue -->
  <!-- 个人信息编辑页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/user/transactions.vue -->
  <!-- 交易记录页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/user/messages.vue -->
  <!-- 消息中心页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/user/settings.vue -->
  <!-- 设置页面 -->
  ```
  - 个人信息编辑页面（头像上传、基本信息、联系方式）
  - 交易记录页面（收支明细、筛选查询、详情查看）
  - 消息中心页面（消息分类、已读未读、消息操作）
  - 设置页面（通知设置、隐私设置、账户安全）

- [ ] **支付功能集成**（1天）
  ```javascript
  // 文件：uniapp/Uni_expert/utils/payment.js
  // 支付工具类

  // 文件：uniapp/Uni_expert/pages/order/payment.vue
  // 支付页面

  // 文件：uniapp/Uni_expert/pages/order/payment-result.vue
  // 支付结果页面
  ```
  - 微信支付调用（调用wx.requestPayment接口）
  - 支付状态处理（支付中、成功、失败状态管理）
  - 支付结果页面（成功/失败页面、订单跳转）
  - 支付失败重试（重新支付、订单查询、状态同步）

- [ ] **消息功能集成**（1天）
  ```vue
  <!-- 文件：uniapp/Uni_expert/pages/message/list.vue -->
  <!-- 消息列表页面 -->

  <!-- 文件：uniapp/Uni_expert/pages/message/detail.vue -->
  <!-- 消息详情页面 -->
  ```
  - 消息列表页面（消息分类、时间排序、批量操作）
  - 消息详情页面（消息内容、相关操作、回复功能）
  - 消息推送处理（接收推送、本地通知、角标更新）
  - 未读消息提醒（红点提示、数量显示、声音提醒）

**预计工作量**: 6天

#### 6. 文件上传功能优化 🚧 **进度: 80%**
**当前状态**: 基础上传功能完成，需要优化和完善

**详细实施计划**:
- [ ] **文件上传优化**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/impl/FileUploadServiceImpl.java
  // 文件上传服务优化

  // 文件：expert/src/main/java/com/qing/expert/util/ImageCompressUtil.java
  // 图片压缩工具类
  ```
  - 文件压缩处理（图片质量压缩、尺寸调整、格式转换）
  - 上传进度显示（前端进度条、上传状态反馈）
  - 批量上传功能（多文件选择、并发上传、进度管理）
  - 文件格式验证（MIME类型检查、文件大小限制、安全检查）

- [ ] **图片处理功能**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/util/ImageProcessUtil.java
  // 图片处理工具类

  // 文件：expert/src/main/java/com/qing/expert/service/ImageService.java
  // 图片服务接口
  ```
  - 图片缩略图生成（多种尺寸、智能裁剪、质量优化）
  - 图片水印添加（文字水印、图片水印、位置控制）
  - 图片格式转换（WebP转换、格式优化、兼容性处理）
  - 图片质量压缩（无损压缩、有损压缩、自适应压缩）

- [ ] **文件管理功能**（1天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/FileManagementService.java
  // 文件管理服务

  // 文件：expert/src/main/java/com/qing/expert/task/FileCleanupTask.java
  // 文件清理定时任务
  ```
  - 文件清理机制（定时清理、垃圾文件检测、存储空间管理）
  - 文件访问统计（下载次数、访问日志、热点文件分析）
  - 文件安全检查（病毒扫描、恶意文件检测、内容审核）
  - 文件备份策略（自动备份、增量备份、异地备份）

**预计工作量**: 5天

#### 6. 文件上传功能优化 🚧 **进度: 80%**
**当前状态**: 基础上传功能完成，需要优化和完善

**待完成任务**:
- [ ] **文件上传优化**
  - 文件压缩处理
  - 上传进度显示
  - 批量上传功能
  - 文件格式验证

- [ ] **图片处理功能**
  - 图片缩略图生成
  - 图片水印添加
  - 图片格式转换
  - 图片质量压缩

- [ ] **文件管理功能**
  - 文件清理机制
  - 文件访问统计
  - 文件安全检查
  - 文件备份策略

**预计工作量**: 2-3天

### 🎯 低优先级任务（优化功能）

#### 7. 系统性能优化 📋 **进度: 0%**
**详细实施计划**:
- [ ] **数据库优化**（2天）
  ```sql
  -- 文件：expert/src/main/resources/sql/performance_optimization.sql
  -- 数据库性能优化脚本

  -- 添加必要索引
  CREATE INDEX idx_order_user_id ON sys_order(user_id);
  CREATE INDEX idx_order_expert_id ON sys_order(expert_id);
  CREATE INDEX idx_order_status ON sys_order(order_status);
  CREATE INDEX idx_order_create_time ON sys_order(create_time);

  -- 复合索引
  CREATE INDEX idx_order_user_status ON sys_order(user_id, order_status);
  CREATE INDEX idx_expert_category_status ON sys_expert(category_id, status);
  ```
  - 添加必要索引（单列索引、复合索引、唯一索引）
  - 优化慢查询（查询分析、执行计划优化、SQL重写）
  - 实现读写分离（主从配置、读写路由、数据同步）
  - 配置数据库连接池（HikariCP优化、连接数调优、超时设置）

- [ ] **缓存策略优化**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/config/RedisConfig.java
  // Redis配置优化

  // 文件：expert/src/main/java/com/qing/expert/service/CacheService.java
  // 缓存服务接口

  // 文件：expert/src/main/java/com/qing/expert/util/CacheKeyUtil.java
  // 缓存键管理工具
  ```
  - Redis缓存策略（缓存层级、过期策略、淘汰策略）
  - 热点数据缓存（用户信息、达人信息、分类数据、配置信息）
  - 缓存更新机制（主动更新、被动更新、定时刷新）
  - 缓存穿透防护（布隆过滤器、空值缓存、参数校验）

- [ ] **接口性能优化**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/aspect/PerformanceMonitorAspect.java
  // 性能监控切面

  // 文件：expert/src/main/java/com/qing/expert/config/AsyncConfig.java
  // 异步处理配置
  ```
  - 接口响应时间优化（代码优化、算法改进、资源优化）
  - 分页查询优化（游标分页、索引优化、缓存分页）
  - 批量操作优化（批量插入、批量更新、事务优化）
  - 异步处理优化（线程池配置、异步注解、消息队列）

**预计工作量**: 6天

#### 8. 安全功能增强 📋 **进度: 0%**
**详细实施计划**:
- [ ] **接口安全加固**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/config/RateLimitConfig.java
  // 接口限流配置

  // 文件：expert/src/main/java/com/qing/expert/aspect/RateLimitAspect.java
  // 限流切面

  // 文件：expert/src/main/java/com/qing/expert/filter/XssFilter.java
  // XSS防护过滤器
  ```
  - 接口访问频率限制（Redis+注解实现、IP限制、用户限制）
  - 参数校验增强（JSR303验证、自定义验证器、参数加密）
  - SQL注入防护（MyBatis防护、参数绑定、特殊字符过滤）
  - XSS攻击防护（输入过滤、输出编码、CSP策略）

- [ ] **数据安全保护**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/util/EncryptUtil.java
  // 加密工具类

  // 文件：expert/src/main/java/com/qing/expert/util/DataMaskUtil.java
  // 数据脱敏工具类

  // 文件：expert/src/main/java/com/qing/expert/service/AuditLogService.java
  // 审计日志服务
  ```
  - 敏感数据加密（AES加密、RSA加密、数据库字段加密）
  - 数据脱敏处理（手机号脱敏、身份证脱敏、地址脱敏）
  - 数据备份加密（备份文件加密、传输加密、存储加密）
  - 访问日志记录（操作日志、访问日志、异常日志）

- [ ] **用户安全功能**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/SecurityService.java
  // 安全服务接口

  // 文件：expert/src/main/java/com/qing/expert/util/LoginSecurityUtil.java
  // 登录安全工具类
  ```
  - 登录异常检测（异地登录、频繁登录、设备识别）
  - 密码强度验证（复杂度检查、常用密码检查、历史密码检查）
  - 账户锁定机制（失败次数限制、锁定时间、解锁机制）
  - 操作日志记录（用户操作、管理员操作、系统操作）

**预计工作量**: 6天

#### 9. 系统监控和运维 📋 **进度: 0%**
**详细实施计划**:
- [ ] **监控系统搭建**（3天）
  ```yaml
  # 文件：docker/monitoring/docker-compose.yml
  # 监控系统Docker配置

  version: '3.8'
  services:
    prometheus:
      image: prom/prometheus
      ports:
        - "9090:9090"

    grafana:
      image: grafana/grafana
      ports:
        - "3000:3000"
  ```
  ```java
  // 文件：expert/src/main/java/com/qing/expert/config/MetricsConfig.java
  // 指标监控配置

  // 文件：expert/src/main/java/com/qing/expert/service/MonitoringService.java
  // 监控服务接口
  ```
  - 应用性能监控（Micrometer+Prometheus、JVM监控、接口性能）
  - 系统资源监控（CPU、内存、磁盘、网络监控）
  - 业务指标监控（订单量、用户量、收入统计、错误率）
  - 告警机制配置（阈值告警、邮件通知、短信通知、钉钉通知）

- [ ] **日志系统优化**（2天）
  ```yaml
  # 文件：docker/logging/docker-compose.yml
  # 日志系统Docker配置

  version: '3.8'
  services:
    elasticsearch:
      image: elasticsearch:7.17.0

    logstash:
      image: logstash:7.17.0

    kibana:
      image: kibana:7.17.0
  ```
  ```java
  // 文件：expert/src/main/java/com/qing/expert/config/LoggingConfig.java
  // 日志配置优化
  ```
  - 日志收集优化（ELK Stack、日志格式标准化、日志分类）
  - 日志分析工具（Kibana仪表板、日志查询、趋势分析）
  - 错误日志告警（实时告警、错误统计、异常分析）
  - 日志归档策略（定期归档、压缩存储、清理策略）

- [ ] **部署自动化**（3天）
  ```dockerfile
  # 文件：expert/Dockerfile
  # 后端应用Docker镜像

  FROM openjdk:17-jdk-slim
  COPY target/expert-1.0.0.jar app.jar
  EXPOSE 9090
  ENTRYPOINT ["java", "-jar", "/app.jar"]
  ```
  ```yaml
  # 文件：.github/workflows/ci-cd.yml
  # GitHub Actions CI/CD配置

  name: CI/CD Pipeline
  on:
    push:
      branches: [ main ]
  jobs:
    build-and-deploy:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v2
        - name: Build and Deploy
          run: |
            # 构建和部署脚本
  ```
  - Docker容器化（多阶段构建、镜像优化、容器编排）
  - CI/CD流水线（GitHub Actions、自动构建、自动部署）
  - 自动化测试（单元测试、集成测试、端到端测试）
  - 蓝绿部署（零停机部署、回滚机制、健康检查）

**预计工作量**: 8天

### 🔍 补充任务（遗漏功能完善）

#### 10. 统计报表功能完善 🚧 **进度: 30%**
**当前状态**: 基础框架已搭建，但大量方法标记为TODO，需要实现真实数据统计

**详细实施计划**:
- [ ] **完善DashboardServiceImpl中的TODO方法**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/impl/DashboardServiceImpl.java

  // 需要实现的方法：
  - getRevenueTrend() // 收入趋势统计
  - getOrderTrend() // 订单趋势统计
  - getUserGrowthTrend() // 用户增长趋势
  - getExpertGrowthTrend() // 达人增长趋势
  ```
  - 实现真实的收入趋势查询（替换模拟数据）
  - 实现订单趋势统计（新增、完成、取消订单统计）
  - 实现用户增长趋势统计
  - 实现达人增长趋势统计

- [ ] **完善FinanceStatisticsServiceImpl中的TODO方法**（2天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/impl/FinanceStatisticsServiceImpl.java

  // 需要实现的方法：
  - getOrderStatistics() // 订单统计
  - getUserBalanceStatistics() // 用户余额统计
  - getWithdrawStatistics() // 提现统计
  - getRevenueStatistics() // 收入统计
  ```
  - 实现订单金额统计（总金额、完成金额、取消金额）
  - 实现用户余额统计（总余额、平均余额、用户数量）
  - 实现提现统计（提现总额、手续费、成功率）
  - 实现平台收入统计（佣金收入、手续费收入）

- [ ] **完善ExpertWorkspaceController中的TODO方法**（1天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/controller/api/ExpertWorkspaceController.java

  // 需要实现的方法：
  - getTodayStatistics() // 今日统计数据
  - getWeeklyStatistics() // 周统计数据
  - getMonthlyStatistics() // 月统计数据
  ```
  - 实现达人今日统计（新订单、完成订单、收入、响应时间）
  - 实现达人周统计和月统计
  - 添加达人业绩排行榜功能

**预计工作量**: 5天

#### 11. 数据导入导出功能开发 📋 **进度: 0%**
**当前状态**: 完全未实现，需要从零开发

**详细实施计划**:
- [ ] **后端导入导出接口开发**（3天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/controller/admin/DataImportExportController.java
  // 数据导入导出控制器

  // 文件：expert/src/main/java/com/qing/expert/service/DataImportExportService.java
  // 数据导入导出服务接口

  // 文件：expert/src/main/java/com/qing/expert/util/ExcelUtil.java
  // Excel处理工具类
  ```
  - 用户数据导入导出（Excel格式）
  - 达人数据导入导出（Excel格式）
  - 订单数据导出（Excel格式，支持筛选条件）
  - 财务数据导出（Excel格式，支持时间范围）
  - 评价数据导出（Excel格式）
  - 提现记录导出（Excel格式）

- [ ] **前端导入导出功能**（2天）
  ```vue
  <!-- 文件：web/src/views/data-management/import-export.vue -->
  <!-- 数据导入导出管理页面 -->

  <!-- 文件：web/src/components/DataExportDialog.vue -->
  <!-- 数据导出对话框组件 -->

  <!-- 文件：web/src/components/DataImportDialog.vue -->
  <!-- 数据导入对话框组件 -->
  ```
  - 数据导出功能（选择数据类型、时间范围、导出格式）
  - 数据导入功能（文件上传、数据预览、导入确认）
  - 导入导出历史记录查看
  - 导入导出进度显示和错误处理

- [ ] **数据模板和验证**（1天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/validator/DataImportValidator.java
  // 数据导入验证器

  // 文件：expert/src/main/java/com/qing/expert/template/ExcelTemplateGenerator.java
  // Excel模板生成器
  ```
  - 创建各种数据的Excel导入模板
  - 实现数据导入验证（格式验证、业务规则验证）
  - 添加导入错误报告功能
  - 支持批量数据处理和事务回滚

**预计工作量**: 6天

#### 12. 分类管理功能完善 🚧 **进度: 70%**
**当前状态**: 后端基本完成，前端管理页面可能不完整

**详细实施计划**:
- [ ] **前端分类管理页面完善**（2天）
  ```vue
  <!-- 文件：web/src/views/categories/index.vue -->
  <!-- 分类管理列表页面 -->

  <!-- 文件：web/src/views/categories/form.vue -->
  <!-- 分类新增编辑页面 -->
  ```
  - 分类列表页面（树形结构、拖拽排序）
  - 分类新增编辑页面（图标选择、层级管理）
  - 分类状态管理（启用/禁用）
  - 分类统计信息（关联达人数量、订单数量）

- [ ] **分类功能优化**（1天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/service/impl/CategoryServiceImpl.java
  // 分类服务优化
  ```
  - 分类层级管理优化
  - 分类图标管理功能
  - 分类排序权重管理
  - 分类删除时的关联数据处理

**预计工作量**: 3天

#### 13. 权限管理细粒度控制 🚧 **进度: 60%**
**当前状态**: 基础权限控制已实现，需要更细粒度的权限管理

**详细实施计划**:
- [ ] **角色权限管理系统**（3天）
  ```java
  // 文件：expert/src/main/java/com/qing/expert/entity/Role.java
  // 角色实体类

  // 文件：expert/src/main/java/com/qing/expert/entity/Permission.java
  // 权限实体类

  // 文件：expert/src/main/java/com/qing/expert/service/RolePermissionService.java
  // 角色权限服务
  ```
  - 创建角色管理功能（超级管理员、普通管理员、客服等）
  - 创建权限管理功能（菜单权限、操作权限、数据权限）
  - 实现角色权限分配功能
  - 添加权限验证注解和切面

- [ ] **前端权限控制**（2天）
  ```vue
  <!-- 文件：web/src/views/system/roles.vue -->
  <!-- 角色管理页面 -->

  <!-- 文件：web/src/views/system/permissions.vue -->
  <!-- 权限管理页面 -->
  ```
  - 角色管理页面（角色列表、权限分配）
  - 权限管理页面（权限树、权限分组）
  - 前端路由权限控制
  - 按钮级别权限控制

**预计工作量**: 5天

### 📊 完整的项目规划

#### 🎯 第一阶段（当前重点）：核心功能完善
**时间安排**: 1周内完成

1. **支付回调接口完善**（2-3天）
   - 微信支付回调验证和处理
   - 移除支付宝支付相关代码
   - 余额支付流程优化

2. **评价系统完善开发**（4-5天）
   - 后端评价接口完整开发
   - 前端评价功能完整实现
   - 评价业务逻辑完善

#### 🔧 第二阶段：重要功能完善
**时间安排**: 3-4周完成

3. **消息推送系统完善**（5天）
   - 微信模板消息实现
   - WebSocket实时推送
   - 小程序订阅消息

4. **前端管理系统完善**（6天）
   - 订单管理页面完善
   - 达人管理页面开发
   - 财务管理页面开发

5. **小程序端功能完善**（6天）
   - 达人工作台优化
   - 用户中心完善
   - 支付功能集成
   - 消息功能集成

6. **文件上传功能优化**（5天）
   - 文件上传优化
   - 图片处理功能
   - 文件管理功能

7. **统计报表功能完善**（5天）
   - 完善DashboardServiceImpl中的TODO方法
   - 完善FinanceStatisticsServiceImpl中的TODO方法
   - 完善ExpertWorkspaceController中的TODO方法

8. **数据导入导出功能开发**（6天）
   - 后端导入导出接口开发
   - 前端导入导出功能
   - 数据模板和验证

9. **分类管理功能完善**（3天）
   - 前端分类管理页面完善
   - 分类功能优化

10. **权限管理细粒度控制**（5天）
    - 角色权限管理系统
    - 前端权限控制

#### 🎯 第三阶段：系统优化
**时间安排**: 2-3周完成

11. **系统性能优化**（6天）
    - 数据库优化
    - 缓存策略优化
    - 接口性能优化

12. **安全功能增强**（6天）
    - 接口安全加固
    - 数据安全保护
    - 用户安全功能

13. **系统监控和运维**（8天）
    - 监控系统搭建
    - 日志系统优化
    - 部署自动化

### 🎯 详细里程碑目标

#### 第13周目标
- **📅 周一-周三**: 完成支付回调接口完善，移除支付宝支付
- **📅 周四-周日**: 开始评价系统开发（后端接口）

#### 第14周目标
- **📅 周一-周三**: 完成评价系统开发（前端功能）
- **📅 周四-周五**: 评价系统测试和优化

#### 第15-16周目标
- **📅 第15周**: 消息推送系统完善
- **📅 第16周**: 前端管理系统完善

#### 第17-18周目标
- **📅 第17周**: 小程序端功能完善
- **📅 第18周**: 文件上传功能优化

#### 第19-20周目标
- **📅 第19周**: 系统性能优化
- **📅 第20周**: 安全功能增强

#### 第21-22周目标
- **📅 第21-22周**: 系统监控和运维、最终测试

### 📈 总工作量统计

| 阶段 | 任务数量 | 预计天数 | 完成时间 |
|------|----------|----------|----------|
| 第一阶段 | 2个核心任务 | 7天 | 1周 |
| 第二阶段 | 8个重要任务 | 41天 | 8-9周 |
| 第三阶段 | 3个优化任务 | 20天 | 4周 |
| **总计** | **13个主要任务** | **68天** | **13-14周** |

### 🔍 补充功能说明

通过深入分析项目代码，我发现了一些重要功能模块在原计划中遗漏或不完整：

#### ✅ 已完成但未在原计划中体现的功能
1. **轮播图管理** - 后端和前端都已完整实现
2. **公告管理** - 后端和前端都已完整实现
3. **提现功能** - 完整的提现申请、审核、转账流程已实现
4. **搜索功能** - 达人搜索、订单搜索等基本功能已实现
5. **文件上传** - 包括进度显示、压缩等高级功能已实现

#### 🚧 需要完善的重要功能
1. **统计报表功能** - 框架已搭建，但大量TODO需要实现真实数据统计
2. **数据导入导出** - 企业级应用必备功能，完全未实现
3. **分类管理** - 后端完成，前端管理页面需要完善
4. **权限管理** - 基础权限有，需要更细粒度的角色权限控制

这些补充功能将使项目更加完整和专业，具备真正的企业级应用水准。

### ⚠️ 风险提醒

1. **微信支付配置**: 需要真实的商户号和证书才能完成测试
2. **消息推送**: 需要微信小程序模板消息审核通过
3. **性能测试**: 需要模拟真实用户量进行压力测试
4. **安全测试**: 建议进行专业的安全渗透测试

---

**开发团队**: 1人全栈开发
**项目周期**: 16周（已完成12周，剩余4周）
**当前状态**: 核心功能基本完成，需要完善细节功能
**技术栈**: Spring Boot 3.5.0 + Vue 3 + UniApp + MySQL + Redis
**部署方式**: Docker容器化部署 + Nginx反向代理
